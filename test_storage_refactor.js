/**
 * 测试存储重构后的功能
 * 这个文件用于验证StorageService的功能是否正常工作
 */

import { storage } from './src/util/storage.js';
import { logger } from './src/util/logger.js';

// 模拟Chrome Storage API
global.chrome = {
  storage: {
    local: {
      data: {},
      set: function(items) {
        return new Promise((resolve) => {
          Object.assign(this.data, items);
          resolve();
        });
      },
      get: function(keys) {
        return new Promise((resolve) => {
          if (keys === null) {
            resolve(this.data);
          } else if (Array.isArray(keys)) {
            const result = {};
            keys.forEach(key => {
              if (this.data.hasOwnProperty(key)) {
                result[key] = this.data[key];
              }
            });
            resolve(result);
          } else {
            const result = {};
            if (this.data.hasOwnProperty(keys)) {
              result[keys] = this.data[keys];
            }
            resolve(result);
          }
        });
      },
      remove: function(keys) {
        return new Promise((resolve) => {
          if (Array.isArray(keys)) {
            keys.forEach(key => delete this.data[key]);
          } else {
            delete this.data[keys];
          }
          resolve();
        });
      }
    }
  }
};

async function testStorageService() {
  console.log('开始测试StorageService...');
  
  try {
    // 测试基本存储功能
    console.log('\n1. 测试基本存储功能');
    await storage.saveData('test_key', 'test_value');
    const value = await storage.get('test_key');
    console.log('保存和获取测试:', value === 'test_value' ? '✅ 通过' : '❌ 失败');
    
    // 测试CSS选择器功能
    console.log('\n2. 测试CSS选择器功能');
    const cssSelector = {
      domain: 'example.com',
      pageType: 'search',
      selector: '.paper-item',
      description: '测试选择器'
    };
    
    await storage.saveData('cssSelectors.example.com_search', cssSelector);
    const retrievedSelector = await storage.getCssSelector('example.com', 'search');
    console.log('CSS选择器测试:', retrievedSelector ? '✅ 通过' : '❌ 失败');
    
    // 测试获取所有数据
    console.log('\n3. 测试获取所有数据');
    const allData = await storage.getAll();
    console.log('获取所有数据测试:', Object.keys(allData).length > 0 ? '✅ 通过' : '❌ 失败');
    
    // 测试多键操作
    console.log('\n4. 测试多键操作');
    await storage.saveData('key1', 'value1');
    await storage.saveData('key2', 'value2');
    const multipleData = await storage.getMultiple(['key1', 'key2']);
    console.log('多键获取测试:', multipleData.key1 === 'value1' && multipleData.key2 === 'value2' ? '✅ 通过' : '❌ 失败');
    
    // 测试删除多个键
    console.log('\n5. 测试删除多个键');
    const removeResult = await storage.removeMultiple(['key1', 'key2']);
    const afterRemove = await storage.getMultiple(['key1', 'key2']);
    console.log('多键删除测试:', removeResult && !afterRemove.key1 && !afterRemove.key2 ? '✅ 通过' : '❌ 失败');
    
    // 测试通用的clearByPrefix方法
    console.log('\n6. 测试clearByPrefix方法');
    const clearResult = await storage.clearByPrefix('cssSelectors.');
    console.log('clearByPrefix测试:', clearResult.success ? '✅ 通过' : '❌ 失败');
    
    console.log('\n✅ 所有测试完成！StorageService重构成功。');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此文件，执行测试
if (typeof require !== 'undefined' && require.main === module) {
  testStorageService();
}

export { testStorageService };
